# Shopify Dynamic Product Page Assignment

## Project Overview
This project implements a fully functional, dynamic product page in Shopify that supports:
- Product media gallery with variant-based image switching
- Dynamic purchase options (Single/Double drink subscriptions)
- Flavor selection with image swatches
- Real-time pricing with subscription discounts
- Dynamic "What's Included" content
- Proper add to cart functionality

## Assignment Requirements

### Core Features
1. **Product Media Gallery**
   - Image carousel with thumbnail navigation
   - Dynamic image updates based on selected variants
   - Responsive design

2. **Purchase Options**
   - Radio buttons for Single/Double drink subscriptions
   - Dynamic UI updates based on selection
   - Variant-based content from Shopify backend

3. **Flavor Selection**
   - Image swatches for Chocolate, Vanilla, Orange
   - Single mode: 1 flavor selector
   - Double mode: 2 flavor selectors
   - Required selections to proceed

4. **Dynamic Pricing**
   - Subscription price: 25% off main price
   - Sales discount: 20% on both regular and subscription
   - Real-time price updates
   - Example: $100 → $75 (subscription) → $60 (final with 20% off)

5. **What's Included Box**
   - Content updates based on selected mode
   - Managed via metafields
   - Shows delivery frequency and benefits

6. **Add to Cart Logic**
   - Default: Single chocolate subscription
   - Accurate variant selection
   - Proper cart reflection

## Technical Stack
- Shopify Liquid templates
- JavaScript (ES6+)
- CSS/SCSS
- Shopify metafields for content management

## Project Structure
```
/
├── assets/
│   ├── product-page.css
│   ├── product-page.js
│   └── images/
├── sections/
│   └── product-form.liquid
├── templates/
│   └── product.liquid
├── snippets/
│   ├── product-media.liquid
│   ├── product-options.liquid
│   └── product-pricing.liquid
└── config/
    └── settings_schema.json
```

## Setup Instructions
1. Create a Shopify development store
2. Upload theme files
3. Configure product variants and metafields
4. Test functionality

## Timeline
Expected duration: 3 days

## Evaluation Criteria
- Accurate Figma design implementation
- Dynamic variant and pricing logic
- Responsive, accessible, performant code
- UI interactivity and state management
- Clean code structure and documentation
