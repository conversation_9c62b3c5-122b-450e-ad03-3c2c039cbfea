{% comment %}
  Product Template with Recommendations Section
{% endcomment %}

<!-- Include CSS and JS for product subscription tabs -->
{{ 'product-subscription-tabs.css' | asset_url | stylesheet_tag }}

<div class="product-page">
  <!-- Product Media Gallery (if you want to add it later) -->
  <div class="product-media">
    {% if product.featured_image %}
      <img src="{{ product.featured_image | img_url: '600x600' }}" 
           alt="{{ product.title }}" 
           class="product-featured-image">
    {% endif %}
  </div>

  <!-- Product Subscription Tabs Inline -->
  <div class="product-subscription-tabs-wrapper">
    {% comment %} This will be replaced with inline code {% endcomment %}
    <p>Section will be added here - please upload the section file first</p>
  </div>

</div>

<!-- Include JavaScript -->
{{ 'product-subscription-tabs.js' | asset_url | script_tag }}

<style>
  .product-page {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
  }

  .product-media {
    text-align: center;
    margin-bottom: 40px;
  }

  .product-featured-image {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
  }

  @media (max-width: 768px) {
    .product-page {
      padding: 16px;
    }
  }
</style>
