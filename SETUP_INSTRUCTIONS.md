# Product Recommendations Setup Instructions

## Overview
This implementation creates a dynamic product page with subscription-based tabs that pull all data from Shopify product variants. The design matches the provided Figma mockup with clean, flat styling (no shadows).

## Files Created
- `sections/product-recommendations.liquid` - Main section template
- `assets/product-recommendations.css` - Styling (flat design, no shadows)
- `assets/product-recommendations.js` - Interactive functionality
- `templates/product.liquid` - Product page template

## Product Setup Requirements

### 1. Product Variant Structure
Your product should have variants with these options:
- **Option 1**: Subscription Type (e.g., "Single Drink Subscription", "Double Drink Subscription")
- **Option 2**: Flavor (e.g., "Chocolate", "Vanilla", "Orange")

### 2. Variant Images
Each variant should have its own featured image that represents the flavor/product combination.

### 3. Pricing Structure
- Set regular prices for each variant
- Use `compare_at_price` for showing crossed-out original prices
- The system will automatically display both current and original prices

## Installation Steps

### 1. Upload Files to Shopify Theme
1. Upload `sections/product-recommendations.liquid` to your theme's `sections/` folder
2. Upload `assets/product-recommendations.css` to your theme's `assets/` folder
3. Upload `assets/product-recommendations.js` to your theme's `assets/` folder
4. Upload `templates/product.liquid` to your theme's `templates/` folder (or modify existing)

### 2. Configure Product Page
If you have an existing product template, you can add the section by including:
```liquid
{% section 'product-recommendations' %}
```

And include the CSS and JS files:
```liquid
{{ 'product-recommendations.css' | asset_url | stylesheet_tag }}
{{ 'product-recommendations.js' | asset_url | script_tag }}
```

### 3. Add Section to Product Page
1. Go to your Shopify admin
2. Navigate to Online Store > Themes
3. Click "Customize" on your theme
4. Go to a product page
5. Add the "Product Recommendations" section
6. Select your product in the section settings

## Features Implemented

### ✅ Dynamic Tab Generation
- Tabs are automatically generated from product variant Option 1 (subscription types)
- Tab names come directly from variant data, not hardcoded

### ✅ Flavor Selection
- Flavor options generated from variant Option 2 (flavors)
- Uses actual variant images for flavor swatches
- Visual selection states with border highlighting

### ✅ Real-time Pricing
- Prices pulled from variant data
- Shows current price and crossed-out original price
- Updates dynamically when selections change

### ✅ What's Included Section
- Shows delivery frequency and product images
- Benefits list (can be customized via metafields)
- Updates based on selected subscription type

### ✅ Add to Cart Functionality
- Automatically selects correct variant based on user choices
- Default selection: First subscription type + first flavor
- Proper error handling and loading states
- Uses Shopify Cart API for seamless integration

### ✅ Responsive Design
- Mobile-friendly layout
- Clean, flat design with no shadows
- Matches provided Figma design

## Customization Options

### Benefits Content
You can customize the benefits list using product metafields:
1. Create custom metafields with keys like:
   - `single_drink_subscription_benefits`
   - `double_drink_subscription_benefits`
2. Use pipe-separated values: `Benefit 1|Benefit 2|Benefit 3`

### Styling Customization
Modify `assets/product-recommendations.css` to adjust:
- Colors (currently using brown header: #B8956A)
- Typography
- Spacing
- Layout

### Delivery Frequency
Currently shows "Every 30 Days" - this can be made dynamic by adding metafields or modifying the template.

## Testing Checklist

### ✅ Tab Functionality
- [ ] Tabs switch correctly
- [ ] Content updates when switching tabs
- [ ] Radio buttons work properly

### ✅ Flavor Selection
- [ ] Flavor images display correctly
- [ ] Selection states work (border highlighting)
- [ ] Flavor names show correctly

### ✅ Pricing
- [ ] Current prices display correctly
- [ ] Original prices show with strikethrough
- [ ] Prices update when changing selections

### ✅ Add to Cart
- [ ] Button enables/disables correctly
- [ ] Correct variant is added to cart
- [ ] Loading and success states work
- [ ] Error handling works

### ✅ Responsive Design
- [ ] Works on mobile devices
- [ ] Layout adapts properly
- [ ] Images scale correctly

## Troubleshooting

### No Tabs Showing
- Check that your product has variants with Option 1 set
- Ensure the section is added to the product page
- Verify the product is selected in section settings

### Images Not Displaying
- Ensure each variant has a featured image
- Check image file formats (JPG, PNG, WebP supported)
- Verify image URLs are accessible

### Add to Cart Not Working
- Check browser console for JavaScript errors
- Ensure variant IDs are being set correctly
- Verify Shopify Cart API is accessible

### Styling Issues
- Check that CSS file is loading correctly
- Verify no conflicting styles from theme
- Use browser developer tools to debug

## Browser Support
- Chrome 60+
- Firefox 60+
- Safari 12+
- Edge 79+

## Performance Notes
- Images are lazy-loaded for better performance
- JavaScript uses modern ES6+ features
- CSS uses efficient selectors
- No external dependencies required
