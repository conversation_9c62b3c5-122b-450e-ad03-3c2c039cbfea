/* Product Subscription Tabs Styles - Clean, flat design with no shadows */

.product-recommendations {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Recommended Header */
.recommendations-header {
  background-color: #B8956A;
  color: white;
  text-align: center;
  padding: 12px 20px;
  margin-bottom: 0;
  border-radius: 8px 8px 0 0;
}

.recommendations-header h2 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0.5px;
}

/* Tab Navigation */
.subscription-tabs {
  border: 1px solid #e0e0e0;
  border-top: none;
  background: white;
}

.tab-item {
  border-bottom: 1px solid #e0e0e0;
  position: relative;
}

.tab-item:last-child {
  border-bottom: none;
  border-radius: 0 0 8px 8px;
}

.tab-item input[type="radio"] {
  display: none;
}

.tab-item label {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.tab-item label:hover {
  background-color: #f8f8f8;
}

.tab-item.active label {
  background-color: #f0f0f0;
}

/* Radio Button Styling */
.radio-button {
  width: 20px;
  height: 20px;
  border: 2px solid #ccc;
  border-radius: 50%;
  margin-right: 12px;
  position: relative;
  flex-shrink: 0;
}

.tab-item input[type="radio"]:checked + label .radio-button {
  border-color: #333;
}

.tab-item input[type="radio"]:checked + label .radio-button::after {
  content: '';
  width: 10px;
  height: 10px;
  background-color: #333;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* Tab Title and Price */
.tab-title {
  flex: 1;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.tab-price {
  display: flex;
  align-items: center;
  gap: 8px;
}

.current-price {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.original-price {
  font-size: 14px;
  color: #999;
  text-decoration: line-through;
}

/* Tab Content */
.tab-content-container {
  border: 1px solid #e0e0e0;
  border-top: none;
  background: white;
  border-radius: 0 0 8px 8px;
}

.tab-content {
  display: none;
  padding: 20px;
}

.tab-content.active {
  display: block;
}

/* Flavor Selection */
.flavor-selection {
  margin-bottom: 30px;
}

.flavor-selection h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.flavor-options {
  display: flex;
  gap: 20px;
  justify-content: center;
}

.flavor-option {
  text-align: center;
  cursor: pointer;
}

.flavor-option input[type="radio"] {
  display: none;
}

.flavor-option label {
  display: block;
  cursor: pointer;
}

.flavor-image {
  display: block;
  margin-bottom: 8px;
  border: 2px solid transparent;
  border-radius: 8px;
  overflow: hidden;
  transition: border-color 0.2s ease;
}

.flavor-option.selected .flavor-image,
.flavor-option input[type="radio"]:checked + label .flavor-image {
  border-color: #333;
}

.flavor-image img {
  width: 80px;
  height: 80px;
  object-fit: cover;
  display: block;
}

.flavor-name {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.flavor-option.selected .flavor-name,
.flavor-option input[type="radio"]:checked + label .flavor-name {
  color: #333;
}

/* What's Included Section */
.whats-included {
  margin-bottom: 30px;
}

.whats-included h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.included-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.delivery-info {
  display: flex;
  gap: 20px;
}

.delivery-frequency,
.onetime-info {
  flex: 1;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  text-align: center;
}

.frequency-text,
.onetime-text {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 12px;
}

.free-text {
  color: #B8956A;
  font-weight: 600;
}

.frequency-images,
.onetime-images {
  display: flex;
  justify-content: center;
  gap: 8px;
}

.frequency-images img,
.onetime-images img {
  width: 40px;
  height: 40px;
  object-fit: cover;
  border-radius: 4px;
}

/* Benefits List */
.benefits-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.benefit-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.checkmark {
  color: #4CAF50;
  font-weight: bold;
  font-size: 14px;
  margin-top: 2px;
  flex-shrink: 0;
}

.benefit-text {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

/* Add to Cart Section */
.add-to-cart-section {
  margin-top: 20px;
}

.add-to-cart-btn {
  width: 100%;
  background-color: #333;
  color: white;
  border: none;
  padding: 16px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.add-to-cart-btn:hover:not(:disabled) {
  background-color: #555;
}

.add-to-cart-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
  .product-recommendations {
    padding: 16px;
  }
  
  .flavor-options {
    gap: 12px;
  }
  
  .flavor-image img {
    width: 60px;
    height: 60px;
  }
  
  .delivery-info {
    flex-direction: column;
    gap: 12px;
  }
  
  .tab-item label {
    padding: 12px 16px;
  }
  
  .tab-title {
    font-size: 14px;
  }
  
  .current-price {
    font-size: 16px;
  }
}
