{% comment %}
  COPY THIS CODE INTO A CUSTOM LIQUID SECTION
  Go to: Theme Customizer > Product Page > Add Section > Custom Liquid
  Then paste this entire code
{% endcomment %}

<style>
/* Product Subscription Tabs Styles - Clean, flat design with no shadows */
.product-recommendations {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.recommendations-header {
  background-color: #B8956A;
  color: white;
  text-align: center;
  padding: 12px 20px;
  margin-bottom: 0;
  border-radius: 8px 8px 0 0;
}

.recommendations-header h2 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.subscription-tabs {
  border: 1px solid #e0e0e0;
  border-top: none;
  background: white;
}

.tab-item {
  border-bottom: 1px solid #e0e0e0;
  position: relative;
}

.tab-item:last-child {
  border-bottom: none;
  border-radius: 0 0 8px 8px;
}

.tab-item input[type="radio"] {
  display: none;
}

.tab-item label {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.tab-item label:hover {
  background-color: #f8f8f8;
}

.tab-item.active label {
  background-color: #f0f0f0;
}

.radio-button {
  width: 20px;
  height: 20px;
  border: 2px solid #ccc;
  border-radius: 50%;
  margin-right: 12px;
  position: relative;
  flex-shrink: 0;
}

.tab-item input[type="radio"]:checked + label .radio-button {
  border-color: #333;
}

.tab-item input[type="radio"]:checked + label .radio-button::after {
  content: '';
  width: 10px;
  height: 10px;
  background-color: #333;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.tab-title {
  flex: 1;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.tab-price {
  display: flex;
  align-items: center;
  gap: 8px;
}

.current-price {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.original-price {
  font-size: 14px;
  color: #999;
  text-decoration: line-through;
}

.tab-content-container {
  border: 1px solid #e0e0e0;
  border-top: none;
  background: white;
  border-radius: 0 0 8px 8px;
}

.tab-content {
  display: none;
  padding: 20px;
}

.tab-content.active {
  display: block;
}

.flavor-selection {
  margin-bottom: 30px;
}

.flavor-selection h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.flavor-options {
  display: flex;
  gap: 20px;
  justify-content: center;
}

.flavor-option {
  text-align: center;
  cursor: pointer;
}

.flavor-option input[type="radio"] {
  display: none;
}

.flavor-option label {
  display: block;
  cursor: pointer;
}

.flavor-image {
  display: block;
  margin-bottom: 8px;
  border: 2px solid transparent;
  border-radius: 8px;
  overflow: hidden;
  transition: border-color 0.2s ease;
}

.flavor-option.selected .flavor-image,
.flavor-option input[type="radio"]:checked + label .flavor-image {
  border-color: #333;
}

.flavor-image img {
  width: 80px;
  height: 80px;
  object-fit: cover;
  display: block;
}

.flavor-name {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.flavor-option.selected .flavor-name,
.flavor-option input[type="radio"]:checked + label .flavor-name {
  color: #333;
}

.whats-included {
  margin-bottom: 30px;
}

.whats-included h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.included-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.delivery-info {
  display: flex;
  gap: 20px;
}

.delivery-frequency,
.onetime-info {
  flex: 1;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  text-align: center;
}

.frequency-text,
.onetime-text {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 12px;
}

.free-text {
  color: #B8956A;
  font-weight: 600;
}

.frequency-images,
.onetime-images {
  display: flex;
  justify-content: center;
  gap: 8px;
}

.frequency-images img,
.onetime-images img {
  width: 40px;
  height: 40px;
  object-fit: cover;
  border-radius: 4px;
}

.benefits-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.benefit-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.checkmark {
  color: #4CAF50;
  font-weight: bold;
  font-size: 14px;
  margin-top: 2px;
  flex-shrink: 0;
}

.benefit-text {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.add-to-cart-section {
  margin-top: 20px;
}

.add-to-cart-btn {
  width: 100%;
  background-color: #333;
  color: white;
  border: none;
  padding: 16px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.add-to-cart-btn:hover:not(:disabled) {
  background-color: #555;
}

.add-to-cart-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .product-recommendations {
    padding: 16px;
  }
  
  .flavor-options {
    gap: 12px;
  }
  
  .flavor-image img {
    width: 60px;
    height: 60px;
  }
  
  .delivery-info {
    flex-direction: column;
    gap: 12px;
  }
  
  .tab-item label {
    padding: 12px 16px;
  }
  
  .tab-title {
    font-size: 14px;
  }
  
  .current-price {
    font-size: 16px;
  }
}
</style>

{%- comment -%} Group variants by subscription type (option1) {%- endcomment -%}
{%- assign subscription_types = '' -%}

{%- for variant in product.variants -%}
  {%- assign subscription_type = variant.option1 -%}
  {%- unless subscription_types contains subscription_type -%}
    {%- assign subscription_types = subscription_types | append: subscription_type | append: ',' -%}
  {%- endunless -%}
{%- endfor -%}

{%- assign subscription_types = subscription_types | split: ',' -%}

<div class="product-recommendations" data-product-id="{{ product.id }}">
  <!-- Recommended Header -->
  <div class="recommendations-header">
    <h2>Recommended</h2>
  </div>

  <!-- Tab Navigation -->
  <div class="subscription-tabs">
    {%- for subscription_type in subscription_types -%}
      {%- unless subscription_type == blank -%}
        {%- assign tab_id = subscription_type | handle -%}
        {%- assign is_first = forloop.first -%}
        
        <div class="tab-item {% if is_first %}active{% endif %}" 
             data-tab="{{ tab_id }}" 
             data-subscription-type="{{ subscription_type }}">
          <input type="radio" 
                 name="subscription_type" 
                 id="tab-{{ tab_id }}" 
                 value="{{ subscription_type }}"
                 {% if is_first %}checked{% endif %}>
          <label for="tab-{{ tab_id }}">
            <span class="radio-button"></span>
            <span class="tab-title">{{ subscription_type }}</span>
            <span class="tab-price">
              {%- for variant in product.variants -%}
                {%- if variant.option1 == subscription_type -%}
                  <span class="current-price">${{ variant.price | money_without_currency }}</span>
                  {%- if variant.compare_at_price > variant.price -%}
                    <span class="original-price">${{ variant.compare_at_price | money_without_currency }}</span>
                  {%- endif -%}
                  {%- break -%}
                {%- endif -%}
              {%- endfor -%}
            </span>
          </label>
        </div>
      {%- endunless -%}
    {%- endfor -%}
  </div>

  <!-- Tab Content -->
  <div class="tab-content-container">
    {%- for subscription_type in subscription_types -%}
      {%- unless subscription_type == blank -%}
        {%- assign tab_id = subscription_type | handle -%}
        {%- assign is_first = forloop.first -%}
        
        <div class="tab-content {% if is_first %}active{% endif %}" 
             data-content="{{ tab_id }}">
          
          <!-- Flavor Selection -->
          <div class="flavor-selection">
            <h3>Choose Flavor</h3>
            <div class="flavor-options">
              {%- assign flavors_added = '' -%}
              {%- for variant in product.variants -%}
                {%- if variant.option1 == subscription_type -%}
                  {%- assign flavor = variant.option2 -%}
                  {%- unless flavors_added contains flavor -%}
                    {%- assign flavor_id = flavor | handle -%}
                    {%- assign is_first_flavor = forloop.first -%}
                    {%- assign flavors_added = flavors_added | append: flavor | append: ',' -%}
                    
                    <div class="flavor-option {% if is_first_flavor %}selected{% endif %}"
                         data-flavor="{{ flavor }}"
                         data-variant-id="{{ variant.id }}">
                      <input type="radio" 
                             name="flavor_{{ tab_id }}" 
                             id="flavor-{{ tab_id }}-{{ flavor_id }}" 
                             value="{{ flavor }}"
                             {% if is_first_flavor %}checked{% endif %}>
                      <label for="flavor-{{ tab_id }}-{{ flavor_id }}">
                        <span class="flavor-image">
                          {%- if variant.featured_image -%}
                            <img src="{{ variant.featured_image | img_url: '100x100' }}" 
                                 alt="{{ flavor }}" 
                                 loading="lazy">
                          {%- else -%}
                            <img src="{{ product.featured_image | img_url: '100x100' }}" 
                                 alt="{{ flavor }}" 
                                 loading="lazy">
                          {%- endif -%}
                        </span>
                        <span class="flavor-name">{{ flavor }}</span>
                      </label>
                    </div>
                  {%- endunless -%}
                {%- endif -%}
              {%- endfor -%}
            </div>
          </div>

          <!-- What's Included Section -->
          <div class="whats-included">
            <h3>What's Included:</h3>
            
            <div class="included-content">
              <div class="delivery-info">
                <div class="delivery-frequency">
                  <span class="frequency-text">Every 30 Days</span>
                  <div class="frequency-images">
                    {%- for variant in product.variants -%}
                      {%- if variant.option1 == subscription_type -%}
                        {%- if variant.featured_image -%}
                          <img src="{{ variant.featured_image | img_url: '80x80' }}" 
                               alt="{{ variant.option2 }}" 
                               loading="lazy">
                        {%- endif -%}
                      {%- endif -%}
                    {%- endfor -%}
                  </div>
                </div>
                
                <div class="onetime-info">
                  <span class="onetime-text">One Time <span class="free-text">(Free)</span></span>
                  <div class="onetime-images">
                    {%- for variant in product.variants -%}
                      {%- if variant.option1 == subscription_type -%}
                        {%- if variant.featured_image -%}
                          <img src="{{ variant.featured_image | img_url: '80x80' }}" 
                               alt="{{ variant.option2 }}" 
                               loading="lazy">
                        {%- endif -%}
                      {%- endif -%}
                    {%- endfor -%}
                  </div>
                </div>
              </div>
              
              <!-- Benefits List -->
              <div class="benefits-list">
                <div class="benefit-item">
                  <span class="checkmark">✓</span>
                  <span class="benefit-text">Premium quality ingredients sourced globally</span>
                </div>
                <div class="benefit-item">
                  <span class="checkmark">✓</span>
                  <span class="benefit-text">Convenient monthly delivery to your doorstep</span>
                </div>
                <div class="benefit-item">
                  <span class="checkmark">✓</span>
                  <span class="benefit-text">Cancel or modify your subscription anytime</span>
                </div>
                <div class="benefit-item">
                  <span class="checkmark">✓</span>
                  <span class="benefit-text">Exclusive subscriber discounts and offers</span>
                </div>
                <div class="benefit-item">
                  <span class="checkmark">✓</span>
                  <span class="benefit-text">24/7 customer support for all your needs</span>
                </div>
              </div>
            </div>
          </div>

        </div>
      {%- endunless -%}
    {%- endfor -%}
  </div>

  <!-- Add to Cart Section -->
  <div class="add-to-cart-section">
    <form action="/cart/add" method="post" enctype="multipart/form-data" class="product-form">
      <input type="hidden" name="id" class="variant-id" value="">
      <button type="submit" class="add-to-cart-btn" disabled>
        <span class="btn-text">Add to Cart</span>
      </button>
    </form>
  </div>

</div>

<script>
// Product Subscription Tabs JavaScript
(function() {
  const container = document.querySelector('.product-recommendations');
  if (!container) return;

  let currentSubscriptionType = null;
  let currentFlavor = null;
  let currentVariant = null;

  // Product variants data
  const variants = [
    {%- for variant in product.variants -%}
      {
        id: {{ variant.id }},
        title: "{{ variant.title | escape }}",
        option1: "{{ variant.option1 | escape }}",
        option2: "{{ variant.option2 | escape }}",
        price: {{ variant.price }},
        compareAtPrice: {{ variant.compare_at_price | default: 0 }},
        available: {{ variant.available | json }}
      }{% unless forloop.last %},{% endunless %}
    {%- endfor -%}
  ];

  function init() {
    bindEvents();
    setDefaultSelections();
    updateAddToCartButton();
  }

  function bindEvents() {
    // Tab switching
    const tabItems = container.querySelectorAll('.tab-item');
    tabItems.forEach(tab => {
      const radio = tab.querySelector('input[type="radio"]');
      radio.addEventListener('change', (e) => {
        if (e.target.checked) {
          switchTab(tab.dataset.subscriptionType, tab.dataset.tab);
        }
      });
    });

    // Flavor selection
    container.addEventListener('change', (e) => {
      if (e.target.name && e.target.name.startsWith('flavor_')) {
        selectFlavor(e.target.value);
      }
    });

    // Form submission
    const form = container.querySelector('.product-form');
    if (form) {
      form.addEventListener('submit', (e) => {
        e.preventDefault();
        addToCart();
      });
    }
  }

  function setDefaultSelections() {
    const firstTab = container.querySelector('.tab-item.active');
    if (firstTab) {
      currentSubscriptionType = firstTab.dataset.subscriptionType;

      const activeTabContent = container.querySelector('.tab-content.active');
      if (activeTabContent) {
        const firstFlavorOption = activeTabContent.querySelector('.flavor-option');
        if (firstFlavorOption) {
          const flavorInput = firstFlavorOption.querySelector('input[type="radio"]');
          if (flavorInput) {
            flavorInput.checked = true;
            currentFlavor = flavorInput.value;
            firstFlavorOption.classList.add('selected');
          }
        }
      }
    }

    updateCurrentVariant();
    updatePricing();
  }

  function switchTab(subscriptionType, tabId) {
    currentSubscriptionType = subscriptionType;

    const allTabs = container.querySelectorAll('.tab-item');
    allTabs.forEach(tab => tab.classList.remove('active'));

    const activeTab = container.querySelector(`[data-tab="${tabId}"]`);
    if (activeTab) {
      activeTab.classList.add('active');
    }

    const allTabContents = container.querySelectorAll('.tab-content');
    allTabContents.forEach(content => content.classList.remove('active'));

    const activeContent = container.querySelector(`[data-content="${tabId}"]`);
    if (activeContent) {
      activeContent.classList.add('active');

      const firstFlavorOption = activeContent.querySelector('.flavor-option');
      if (firstFlavorOption) {
        const flavorInput = firstFlavorOption.querySelector('input[type="radio"]');
        if (flavorInput) {
          flavorInput.checked = true;
          currentFlavor = flavorInput.value;
          updateFlavorSelection(activeContent, flavorInput.value);
        }
      }
    }

    updateCurrentVariant();
    updatePricing();
    updateAddToCartButton();
  }

  function selectFlavor(flavor) {
    currentFlavor = flavor;

    const activeTabContent = container.querySelector('.tab-content.active');
    if (activeTabContent) {
      updateFlavorSelection(activeTabContent, flavor);
    }

    updateCurrentVariant();
    updatePricing();
    updateAddToCartButton();
  }

  function updateFlavorSelection(tabContent, selectedFlavor) {
    const flavorOptions = tabContent.querySelectorAll('.flavor-option');
    flavorOptions.forEach(option => {
      option.classList.remove('selected');
      const input = option.querySelector('input[type="radio"]');
      if (input && input.value === selectedFlavor) {
        option.classList.add('selected');
      }
    });
  }

  function updateCurrentVariant() {
    if (!currentSubscriptionType || !currentFlavor) {
      currentVariant = null;
      return;
    }

    currentVariant = variants.find(variant =>
      variant.option1 === currentSubscriptionType &&
      variant.option2 === currentFlavor
    );
  }

  function updatePricing() {
    if (!currentVariant) return;

    const activeTab = container.querySelector('.tab-item.active');
    if (activeTab) {
      const priceContainer = activeTab.querySelector('.tab-price');
      if (priceContainer) {
        const currentPriceEl = priceContainer.querySelector('.current-price');
        const originalPriceEl = priceContainer.querySelector('.original-price');

        if (currentPriceEl) {
          const price = (currentVariant.price / 100).toFixed(2);
          currentPriceEl.textContent = `$${price}`;
        }

        if (originalPriceEl && currentVariant.compareAtPrice > 0) {
          const comparePrice = (currentVariant.compareAtPrice / 100).toFixed(2);
          originalPriceEl.textContent = `$${comparePrice}`;
          originalPriceEl.style.display = 'inline';
        } else if (originalPriceEl) {
          originalPriceEl.style.display = 'none';
        }
      }
    }
  }

  function updateAddToCartButton() {
    const variantIdInput = container.querySelector('.variant-id');
    const addToCartBtn = container.querySelector('.add-to-cart-btn');

    if (!currentVariant || !currentVariant.available) {
      if (addToCartBtn) {
        addToCartBtn.disabled = true;
        addToCartBtn.querySelector('.btn-text').textContent = 'Unavailable';
      }
      if (variantIdInput) {
        variantIdInput.value = '';
      }
      return;
    }

    if (variantIdInput) {
      variantIdInput.value = currentVariant.id;
    }

    if (addToCartBtn) {
      addToCartBtn.disabled = false;
      addToCartBtn.querySelector('.btn-text').textContent = 'Add to Cart';
    }
  }

  async function addToCart() {
    if (!currentVariant || !currentVariant.available) {
      console.error('No valid variant selected');
      return;
    }

    const addToCartBtn = container.querySelector('.add-to-cart-btn');
    const btnText = addToCartBtn.querySelector('.btn-text');
    const originalText = btnText.textContent;

    try {
      addToCartBtn.disabled = true;
      btnText.textContent = 'Adding...';

      const response = await fetch('/cart/add.js', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: currentVariant.id,
          quantity: 1
        })
      });

      if (response.ok) {
        btnText.textContent = 'Added!';
        document.dispatchEvent(new CustomEvent('cart:updated'));

        setTimeout(() => {
          btnText.textContent = originalText;
          addToCartBtn.disabled = false;
        }, 2000);
      } else {
        throw new Error('Failed to add to cart');
      }
    } catch (error) {
      console.error('Error adding to cart:', error);
      btnText.textContent = 'Error - Try Again';

      setTimeout(() => {
        btnText.textContent = originalText;
        addToCartBtn.disabled = false;
      }, 3000);
    }
  }

  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
  } else {
    init();
  }
})();
</script>
