{% comment %}
  ACCORDION DESIGN - REAL SHOPIFY PRODUCT INTEGRATION
  Copy this into Custom Liquid section
  Features: Real product data, accordion UI, no add to cart button
{% endcomment %}

<style>
/* Product Subscription Accordion - Clean Design with Real Shopify Data */
.product-recommendations {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.recommendations-header {
  background-color: #B8956A;
  color: white;
  text-align: center;
  padding: 12px 20px;
  margin-bottom: 0;
  border-radius: 8px 8px 0 0;
}

.recommendations-header h2 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0.5px;
}

/* Accordion Container */
.subscription-accordion {
  border: 1px solid #e0e0e0;
  border-top: none;
  background: white;
  border-radius: 0 0 8px 8px;
}

/* Accordion Item */
.accordion-item {
  border-bottom: 1px solid #e0e0e0;
}

.accordion-item:last-child {
  border-bottom: none;
}

/* Accordion Header (Clickable) */
.accordion-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  background: white;
}

.accordion-header:hover {
  background-color: #f8f8f8;
}

.accordion-item.active .accordion-header {
  background-color: #f0f0f0;
}

/* Radio Button */
.accordion-radio {
  width: 20px;
  height: 20px;
  border: 2px solid #ccc;
  border-radius: 50%;
  margin-right: 12px;
  position: relative;
  flex-shrink: 0;
  transition: border-color 0.2s ease;
}

.accordion-item.active .accordion-radio {
  border-color: #333;
}

.accordion-item.active .accordion-radio::after {
  content: '';
  width: 10px;
  height: 10px;
  background-color: #333;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* Accordion Title */
.accordion-title {
  flex: 1;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

/* Accordion Price */
.accordion-price {
  display: flex;
  align-items: center;
  gap: 8px;
}

.current-price {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.original-price {
  font-size: 14px;
  color: #999;
  text-decoration: line-through;
}

/* Accordion Content */
.accordion-content {
  display: none;
  padding: 20px;
  border-top: 1px solid #e0e0e0;
  background: #fafafa;
}

.accordion-item.active .accordion-content {
  display: block;
}

.flavor-selection {
  margin-bottom: 30px;
}

.flavor-selection h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.flavor-options {
  display: flex;
  gap: 20px;
  justify-content: center;
}

.flavor-option {
  text-align: center;
  cursor: pointer;
}

.flavor-option input[type="radio"] {
  display: none;
}

.flavor-option label {
  display: block;
  cursor: pointer;
}

.flavor-image {
  display: block;
  margin-bottom: 8px;
  border: 2px solid transparent;
  border-radius: 8px;
  overflow: hidden;
  transition: border-color 0.2s ease;
}

.flavor-option.selected .flavor-image,
.flavor-option input[type="radio"]:checked + label .flavor-image {
  border-color: #333;
}

.flavor-image img {
  width: 80px;
  height: 80px;
  object-fit: cover;
  display: block;
}

.flavor-name {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.flavor-option.selected .flavor-name,
.flavor-option input[type="radio"]:checked + label .flavor-name {
  color: #333;
}

.whats-included {
  margin-bottom: 30px;
}

.whats-included h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.included-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.delivery-info {
  display: flex;
  gap: 20px;
}

.delivery-frequency,
.onetime-info {
  flex: 1;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  text-align: center;
}

.frequency-text,
.onetime-text {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 12px;
}

.free-text {
  color: #B8956A;
  font-weight: 600;
}

.frequency-images,
.onetime-images {
  display: flex;
  justify-content: center;
  gap: 8px;
}

.frequency-images img,
.onetime-images img {
  width: 40px;
  height: 40px;
  object-fit: cover;
  border-radius: 4px;
}

.benefits-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.benefit-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.checkmark {
  color: #4CAF50;
  font-weight: bold;
  font-size: 14px;
  margin-top: 2px;
  flex-shrink: 0;
}

.benefit-text {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}



@media (max-width: 768px) {
  .product-recommendations {
    padding: 16px;
  }
  
  .flavor-options {
    gap: 12px;
  }
  
  .flavor-image img {
    width: 60px;
    height: 60px;
  }
  
  .delivery-info {
    flex-direction: column;
    gap: 12px;
  }
  
  .accordion-header {
    padding: 12px 16px;
  }

  .accordion-title {
    font-size: 14px;
  }
  
  .current-price {
    font-size: 16px;
  }
}
</style>

{%- comment -%} Group variants by subscription type (option1) {%- endcomment -%}
{%- assign subscription_types = '' -%}
{%- assign single_variants = '' -%}
{%- assign double_variants = '' -%}

{%- for variant in product.variants -%}
  {%- assign subscription_type = variant.option1 -%}
  {%- unless subscription_types contains subscription_type -%}
    {%- assign subscription_types = subscription_types | append: subscription_type | append: ',' -%}
  {%- endunless -%}

  {%- if subscription_type contains 'Single' -%}
    {%- assign single_variants = single_variants | append: variant.id | append: ',' -%}
  {%- elsif subscription_type contains 'Double' -%}
    {%- assign double_variants = double_variants | append: variant.id | append: ',' -%}
  {%- endif -%}
{%- endfor -%}

{%- assign subscription_types = subscription_types | split: ',' -%}

<div class="product-recommendations" data-product-id="{{ product.id }}">
  <!-- Recommended Header -->
  <div class="recommendations-header">
    <h2>Recommended</h2>
  </div>

  <!-- Accordion Container -->
  <div class="subscription-accordion">
    {%- for subscription_type in subscription_types -%}
      {%- unless subscription_type == blank -%}
        {%- assign tab_id = subscription_type | handle -%}
        {%- assign is_first = forloop.first -%}

        <div class="accordion-item {% if is_first %}active{% endif %}"
             data-tab="{{ tab_id }}"
             data-subscription-type="{{ subscription_type }}">

          <div class="accordion-header" onclick="toggleAccordion(this)">
            <div class="accordion-radio"></div>
            <div class="accordion-title">{{ subscription_type }}</div>
            <div class="accordion-price">
              {%- comment -%} Get first variant price for this subscription type {%- endcomment -%}
              {%- assign first_variant_found = false -%}
              {%- for variant in product.variants -%}
                {%- if variant.option1 == subscription_type and first_variant_found == false -%}
                  <span class="current-price">${{ variant.price | money_without_currency }}</span>
                  {%- if variant.compare_at_price > variant.price -%}
                    <span class="original-price">${{ variant.compare_at_price | money_without_currency }}</span>
                  {%- endif -%}
                  {%- assign first_variant_found = true -%}
                {%- endif -%}
              {%- endfor -%}
            </div>
          </div>

          <div class="accordion-content" data-content="{{ tab_id }}">
          
          <!-- Flavor Selection -->
          <div class="flavor-selection">
            <h3>Choose Flavor</h3>
            <div class="flavor-options">
              {%- assign flavors_for_this_tab = '' -%}
              {%- assign flavor_count = 0 -%}

              {%- comment -%} First pass: collect unique flavors for this subscription type {%- endcomment -%}
              {%- for variant in product.variants -%}
                {%- if variant.option1 == subscription_type -%}
                  {%- assign flavor = variant.option2 -%}
                  {%- unless flavors_for_this_tab contains flavor -%}
                    {%- assign flavors_for_this_tab = flavors_for_this_tab | append: flavor | append: '|' -%}
                  {%- endunless -%}
                {%- endif -%}
              {%- endfor -%}

              {%- assign unique_flavors = flavors_for_this_tab | split: '|' -%}

              {%- comment -%} Second pass: create flavor options {%- endcomment -%}
              {%- for flavor in unique_flavors -%}
                {%- unless flavor == blank -%}
                  {%- assign flavor_id = flavor | handle -%}
                  {%- assign is_first_flavor = forloop.first -%}

                  {%- comment -%} Find the variant for this flavor and subscription type {%- endcomment -%}
                  {%- for variant in product.variants -%}
                    {%- if variant.option1 == subscription_type and variant.option2 == flavor -%}
                      <div class="flavor-option {% if is_first_flavor %}selected{% endif %}"
                           data-flavor="{{ flavor }}"
                           data-variant-id="{{ variant.id }}">
                        <input type="radio"
                               name="flavor_{{ tab_id }}"
                               id="flavor-{{ tab_id }}-{{ flavor_id }}"
                               value="{{ flavor }}"
                               {% if is_first_flavor %}checked{% endif %}>
                        <label for="flavor-{{ tab_id }}-{{ flavor_id }}">
                          <span class="flavor-image">
                            {%- if variant.featured_image -%}
                              <img src="{{ variant.featured_image | img_url: '100x100' }}"
                                   alt="{{ flavor }}"
                                   loading="lazy">
                            {%- else -%}
                              <img src="{{ product.featured_image | img_url: '100x100' }}"
                                   alt="{{ flavor }}"
                                   loading="lazy">
                            {%- endif -%}
                          </span>
                          <span class="flavor-name">{{ flavor }}</span>
                        </label>
                      </div>
                      {%- break -%}
                    {%- endif -%}
                  {%- endfor -%}
                {%- endunless -%}
              {%- endfor -%}
            </div>
          </div>

          <!-- What's Included Section -->
          <div class="whats-included">
            <h3>What's Included:</h3>
            
            <div class="included-content">
              <div class="delivery-info">
                <div class="delivery-frequency">
                  <span class="frequency-text">Every 30 Days</span>
                  <div class="frequency-images">
                    {%- for variant in product.variants -%}
                      {%- if variant.option1 == subscription_type -%}
                        {%- if variant.featured_image -%}
                          <img src="{{ variant.featured_image | img_url: '80x80' }}" 
                               alt="{{ variant.option2 }}" 
                               loading="lazy">
                        {%- endif -%}
                      {%- endif -%}
                    {%- endfor -%}
                  </div>
                </div>
                
                <div class="onetime-info">
                  <span class="onetime-text">One Time <span class="free-text">(Free)</span></span>
                  <div class="onetime-images">
                    {%- for variant in product.variants -%}
                      {%- if variant.option1 == subscription_type -%}
                        {%- if variant.featured_image -%}
                          <img src="{{ variant.featured_image | img_url: '80x80' }}" 
                               alt="{{ variant.option2 }}" 
                               loading="lazy">
                        {%- endif -%}
                      {%- endif -%}
                    {%- endfor -%}
                  </div>
                </div>
              </div>
              
              <!-- Benefits List -->
              <div class="benefits-list">
                <div class="benefit-item">
                  <span class="checkmark">✓</span>
                  <span class="benefit-text">Premium quality ingredients sourced globally</span>
                </div>
                <div class="benefit-item">
                  <span class="checkmark">✓</span>
                  <span class="benefit-text">Convenient monthly delivery to your doorstep</span>
                </div>
              </div>
            </div>
          </div>

          </div>
        </div>
      {%- endunless -%}
    {%- endfor -%}
  </div>



</div>

<script>
// Product Subscription Accordion JavaScript
(function() {
  const container = document.querySelector('.product-recommendations');
  if (!container) return;

  let currentSubscriptionType = null;
  let currentFlavor = null;
  let currentVariant = null;

  // Product variants data
  const variants = [
    {%- for variant in product.variants -%}
      {
        id: {{ variant.id }},
        title: "{{ variant.title | escape }}",
        option1: "{{ variant.option1 | escape }}",
        option2: "{{ variant.option2 | escape }}",
        price: {{ variant.price }},
        compareAtPrice: {{ variant.compare_at_price | default: 0 }},
        available: {{ variant.available | json }}
      }{% unless forloop.last %},{% endunless %}
    {%- endfor -%}
  ];

  function init() {
    bindEvents();
    setDefaultSelections();
  }

  function bindEvents() {
    // Flavor selection
    container.addEventListener('change', (e) => {
      if (e.target.name && e.target.name.startsWith('flavor_')) {
        selectFlavor(e.target.value);
      }
    });
  }

  // Accordion toggle function (called from HTML onclick)
  window.toggleAccordion = function(header) {
    const accordionItem = header.parentElement;
    const allItems = container.querySelectorAll('.accordion-item');

    // Close all other accordions
    allItems.forEach(item => {
      if (item !== accordionItem) {
        item.classList.remove('active');
      }
    });

    // Toggle current accordion
    accordionItem.classList.add('active');

    // Update current subscription type
    currentSubscriptionType = accordionItem.dataset.subscriptionType;

    // Set default flavor for the new accordion
    const activeContent = accordionItem.querySelector('.accordion-content');
    if (activeContent) {
      const firstFlavorOption = activeContent.querySelector('.flavor-option');
      if (firstFlavorOption) {
        const flavorInput = firstFlavorOption.querySelector('input[type="radio"]');
        if (flavorInput) {
          flavorInput.checked = true;
          currentFlavor = flavorInput.value;
          updateFlavorSelection(activeContent, flavorInput.value);
        }
      }
    }

    updateCurrentVariant();
    updatePricing();
  };

  function setDefaultSelections() {
    const firstAccordion = container.querySelector('.accordion-item.active');
    if (firstAccordion) {
      currentSubscriptionType = firstAccordion.dataset.subscriptionType;

      const activeContent = firstAccordion.querySelector('.accordion-content');
      if (activeContent) {
        const firstFlavorOption = activeContent.querySelector('.flavor-option');
        if (firstFlavorOption) {
          const flavorInput = firstFlavorOption.querySelector('input[type="radio"]');
          if (flavorInput) {
            flavorInput.checked = true;
            currentFlavor = flavorInput.value;
            firstFlavorOption.classList.add('selected');
          }
        }
      }
    }

    updateCurrentVariant();
    updatePricing();
  }



  function selectFlavor(flavor) {
    currentFlavor = flavor;

    const activeAccordionContent = container.querySelector('.accordion-item.active .accordion-content');
    if (activeAccordionContent) {
      updateFlavorSelection(activeAccordionContent, flavor);
    }

    updateCurrentVariant();
    updatePricing();
  }

  function updateFlavorSelection(accordionContent, selectedFlavor) {
    const flavorOptions = accordionContent.querySelectorAll('.flavor-option');
    flavorOptions.forEach(option => {
      option.classList.remove('selected');
      const input = option.querySelector('input[type="radio"]');
      if (input && input.value === selectedFlavor) {
        option.classList.add('selected');
      }
    });
  }

  function updateCurrentVariant() {
    if (!currentSubscriptionType || !currentFlavor) {
      currentVariant = null;
      return;
    }

    currentVariant = variants.find(variant =>
      variant.option1 === currentSubscriptionType &&
      variant.option2 === currentFlavor
    );
  }

  function updatePricing() {
    if (!currentVariant) return;

    const activeAccordion = container.querySelector('.accordion-item.active');
    if (activeAccordion) {
      const priceContainer = activeAccordion.querySelector('.accordion-price');
      if (priceContainer) {
        const currentPriceEl = priceContainer.querySelector('.current-price');
        const originalPriceEl = priceContainer.querySelector('.original-price');

        if (currentPriceEl) {
          const price = (currentVariant.price / 100).toFixed(2);
          currentPriceEl.textContent = `$${price}`;
        }

        if (originalPriceEl && currentVariant.compareAtPrice > 0) {
          const comparePrice = (currentVariant.compareAtPrice / 100).toFixed(2);
          originalPriceEl.textContent = `$${comparePrice}`;
          originalPriceEl.style.display = 'inline';
        } else if (originalPriceEl) {
          originalPriceEl.style.display = 'none';
        }
      }
    }
  }



  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
  } else {
    init();
  }
})();
</script>
