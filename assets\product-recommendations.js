/**
 * Product Recommendations JavaScript
 * Handles tab switching, flavor selection, pricing updates, and add to cart functionality
 */

class ProductRecommendations {
  constructor() {
    this.container = document.querySelector('.product-recommendations');
    if (!this.container) return;

    this.productData = window.productRecommendationsData || {};
    this.variants = this.productData.variants || [];
    
    this.currentSubscriptionType = null;
    this.currentFlavor = null;
    this.currentVariant = null;

    this.init();
  }

  init() {
    this.bindEvents();
    this.setDefaultSelections();
    this.updateAddToCartButton();
  }

  bindEvents() {
    // Tab switching
    const tabItems = this.container.querySelectorAll('.tab-item');
    tabItems.forEach(tab => {
      const radio = tab.querySelector('input[type="radio"]');
      radio.addEventListener('change', (e) => {
        if (e.target.checked) {
          this.switchTab(tab.dataset.subscriptionType, tab.dataset.tab);
        }
      });
    });

    // Flavor selection
    this.container.addEventListener('change', (e) => {
      if (e.target.name && e.target.name.startsWith('flavor_')) {
        this.selectFlavor(e.target.value);
      }
    });

    // Form submission
    const form = this.container.querySelector('.product-form');
    if (form) {
      form.addEventListener('submit', (e) => {
        e.preventDefault();
        this.addToCart();
      });
    }
  }

  setDefaultSelections() {
    // Set default to first subscription type and first flavor
    const firstTab = this.container.querySelector('.tab-item.active');
    if (firstTab) {
      this.currentSubscriptionType = firstTab.dataset.subscriptionType;
      
      // Set default flavor for the active tab
      const activeTabContent = this.container.querySelector('.tab-content.active');
      if (activeTabContent) {
        const firstFlavorOption = activeTabContent.querySelector('.flavor-option');
        if (firstFlavorOption) {
          const flavorInput = firstFlavorOption.querySelector('input[type="radio"]');
          if (flavorInput) {
            flavorInput.checked = true;
            this.currentFlavor = flavorInput.value;
            firstFlavorOption.classList.add('selected');
          }
        }
      }
    }

    this.updateCurrentVariant();
    this.updatePricing();
  }

  switchTab(subscriptionType, tabId) {
    // Update current subscription type
    this.currentSubscriptionType = subscriptionType;

    // Update tab active states
    const allTabs = this.container.querySelectorAll('.tab-item');
    allTabs.forEach(tab => {
      tab.classList.remove('active');
    });
    
    const activeTab = this.container.querySelector(`[data-tab="${tabId}"]`);
    if (activeTab) {
      activeTab.classList.add('active');
    }

    // Update tab content active states
    const allTabContents = this.container.querySelectorAll('.tab-content');
    allTabContents.forEach(content => {
      content.classList.remove('active');
    });

    const activeContent = this.container.querySelector(`[data-content="${tabId}"]`);
    if (activeContent) {
      activeContent.classList.add('active');
      
      // Set default flavor for the new tab
      const firstFlavorOption = activeContent.querySelector('.flavor-option');
      if (firstFlavorOption) {
        const flavorInput = firstFlavorOption.querySelector('input[type="radio"]');
        if (flavorInput) {
          flavorInput.checked = true;
          this.currentFlavor = flavorInput.value;
          
          // Update flavor selection visual state
          this.updateFlavorSelection(activeContent, flavorInput.value);
        }
      }
    }

    this.updateCurrentVariant();
    this.updatePricing();
    this.updateAddToCartButton();
  }

  selectFlavor(flavor) {
    this.currentFlavor = flavor;
    
    // Update visual selection state
    const activeTabContent = this.container.querySelector('.tab-content.active');
    if (activeTabContent) {
      this.updateFlavorSelection(activeTabContent, flavor);
    }

    this.updateCurrentVariant();
    this.updatePricing();
    this.updateAddToCartButton();
  }

  updateFlavorSelection(tabContent, selectedFlavor) {
    const flavorOptions = tabContent.querySelectorAll('.flavor-option');
    flavorOptions.forEach(option => {
      option.classList.remove('selected');
      const input = option.querySelector('input[type="radio"]');
      if (input && input.value === selectedFlavor) {
        option.classList.add('selected');
      }
    });
  }

  updateCurrentVariant() {
    if (!this.currentSubscriptionType || !this.currentFlavor) {
      this.currentVariant = null;
      return;
    }

    // Find the variant that matches current subscription type and flavor
    this.currentVariant = this.variants.find(variant => 
      variant.option1 === this.currentSubscriptionType && 
      variant.option2 === this.currentFlavor
    );

    console.log('Current variant:', this.currentVariant);
  }

  updatePricing() {
    if (!this.currentVariant) return;

    // Update the price in the active tab
    const activeTab = this.container.querySelector('.tab-item.active');
    if (activeTab) {
      const priceContainer = activeTab.querySelector('.tab-price');
      if (priceContainer) {
        const currentPriceEl = priceContainer.querySelector('.current-price');
        const originalPriceEl = priceContainer.querySelector('.original-price');

        if (currentPriceEl) {
          const price = (this.currentVariant.price / 100).toFixed(2);
          currentPriceEl.textContent = `$${price}`;
        }

        if (originalPriceEl && this.currentVariant.compareAtPrice > 0) {
          const comparePrice = (this.currentVariant.compareAtPrice / 100).toFixed(2);
          originalPriceEl.textContent = `$${comparePrice}`;
          originalPriceEl.style.display = 'inline';
        } else if (originalPriceEl) {
          originalPriceEl.style.display = 'none';
        }
      }
    }
  }

  updateAddToCartButton() {
    const variantIdInput = this.container.querySelector('.variant-id');
    const addToCartBtn = this.container.querySelector('.add-to-cart-btn');

    if (!this.currentVariant || !this.currentVariant.available) {
      if (addToCartBtn) {
        addToCartBtn.disabled = true;
        addToCartBtn.querySelector('.btn-text').textContent = 'Unavailable';
      }
      if (variantIdInput) {
        variantIdInput.value = '';
      }
      return;
    }

    if (variantIdInput) {
      variantIdInput.value = this.currentVariant.id;
    }

    if (addToCartBtn) {
      addToCartBtn.disabled = false;
      addToCartBtn.querySelector('.btn-text').textContent = 'Add to Cart';
    }
  }

  async addToCart() {
    if (!this.currentVariant || !this.currentVariant.available) {
      console.error('No valid variant selected');
      return;
    }

    const addToCartBtn = this.container.querySelector('.add-to-cart-btn');
    const btnText = addToCartBtn.querySelector('.btn-text');
    const originalText = btnText.textContent;

    try {
      // Update button state
      addToCartBtn.disabled = true;
      btnText.textContent = 'Adding...';

      // Add to cart via Shopify Cart API
      const response = await fetch('/cart/add.js', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: this.currentVariant.id,
          quantity: 1
        })
      });

      if (response.ok) {
        const result = await response.json();
        console.log('Added to cart:', result);
        
        // Show success state
        btnText.textContent = 'Added!';
        
        // Trigger cart update event
        document.dispatchEvent(new CustomEvent('cart:updated'));
        
        // Reset button after 2 seconds
        setTimeout(() => {
          btnText.textContent = originalText;
          addToCartBtn.disabled = false;
        }, 2000);

      } else {
        throw new Error('Failed to add to cart');
      }

    } catch (error) {
      console.error('Error adding to cart:', error);
      
      // Show error state
      btnText.textContent = 'Error - Try Again';
      
      // Reset button after 3 seconds
      setTimeout(() => {
        btnText.textContent = originalText;
        addToCartBtn.disabled = false;
      }, 3000);
    }
  }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  new ProductRecommendations();
});

// Also initialize if script loads after DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    new ProductRecommendations();
  });
} else {
  new ProductRecommendations();
}
