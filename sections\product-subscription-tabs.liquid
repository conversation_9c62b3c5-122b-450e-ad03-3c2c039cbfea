{% comment %}
  Product Subscription Tabs Section
  Dynamic tabs based on product variants (subscription types)
  All data pulled from Shopify product variants
{% endcomment %}

{%- assign product = section.settings.product -%}
{%- if product == blank -%}
  {%- assign product = product -%}
{%- endif -%}

{%- if product -%}
  {%- comment -%} Group variants by subscription type (option1) {%- endcomment -%}
  {%- assign subscription_types = '' -%}
  {%- assign single_variants = '' -%}
  {%- assign double_variants = '' -%}
  
  {%- for variant in product.variants -%}
    {%- assign subscription_type = variant.option1 -%}
    {%- unless subscription_types contains subscription_type -%}
      {%- assign subscription_types = subscription_types | append: subscription_type | append: ',' -%}
    {%- endunless -%}
    
    {%- if subscription_type == 'Single Drink Subscription' -%}
      {%- assign single_variants = single_variants | append: variant.id | append: ',' -%}
    {%- elsif subscription_type == 'Double Drink Subscription' -%}
      {%- assign double_variants = double_variants | append: variant.id | append: ',' -%}
    {%- endif -%}
  {%- endfor -%}
  
  {%- assign subscription_types = subscription_types | split: ',' -%}
  {%- assign single_variant_ids = single_variants | split: ',' -%}
  {%- assign double_variant_ids = double_variants | split: ',' -%}

  <div class="product-recommendations" data-product-id="{{ product.id }}">
    <!-- Recommended Header -->
    <div class="recommendations-header">
      <h2>Recommended</h2>
    </div>

    <!-- Tab Navigation -->
    <div class="subscription-tabs">
      {%- for subscription_type in subscription_types -%}
        {%- unless subscription_type == blank -%}
          {%- assign tab_id = subscription_type | handle -%}
          {%- assign is_first = forloop.first -%}
          
          <div class="tab-item {% if is_first %}active{% endif %}" 
               data-tab="{{ tab_id }}" 
               data-subscription-type="{{ subscription_type }}">
            <input type="radio" 
                   name="subscription_type" 
                   id="tab-{{ tab_id }}" 
                   value="{{ subscription_type }}"
                   {% if is_first %}checked{% endif %}>
            <label for="tab-{{ tab_id }}">
              <span class="radio-button"></span>
              <span class="tab-title">{{ subscription_type }}</span>
              <span class="tab-price">
                {%- comment -%} Get price for first variant of this subscription type {%- endcomment -%}
                {%- for variant in product.variants -%}
                  {%- if variant.option1 == subscription_type -%}
                    <span class="current-price">${{ variant.price | money_without_currency }}</span>
                    {%- if variant.compare_at_price > variant.price -%}
                      <span class="original-price">${{ variant.compare_at_price | money_without_currency }}</span>
                    {%- endif -%}
                    {%- break -%}
                  {%- endif -%}
                {%- endfor -%}
              </span>
            </label>
          </div>
        {%- endunless -%}
      {%- endfor -%}
    </div>

    <!-- Tab Content -->
    <div class="tab-content-container">
      {%- for subscription_type in subscription_types -%}
        {%- unless subscription_type == blank -%}
          {%- assign tab_id = subscription_type | handle -%}
          {%- assign is_first = forloop.first -%}
          
          <div class="tab-content {% if is_first %}active{% endif %}" 
               data-content="{{ tab_id }}">
            
            <!-- Flavor Selection -->
            <div class="flavor-selection">
              <h3>Choose Flavor</h3>
              <div class="flavor-options">
                {%- assign flavors_added = '' -%}
                {%- for variant in product.variants -%}
                  {%- if variant.option1 == subscription_type -%}
                    {%- assign flavor = variant.option2 -%}
                    {%- unless flavors_added contains flavor -%}
                      {%- assign flavors_added = flavors_added | append: flavor | append: ',' -%}
                      {%- assign flavor_id = flavor | handle -%}
                      {%- assign is_first_flavor = flavors_added == flavor | append: ',' -%}
                      
                      <div class="flavor-option {% if is_first_flavor %}selected{% endif %}"
                           data-flavor="{{ flavor }}"
                           data-variant-id="{{ variant.id }}">
                        <input type="radio" 
                               name="flavor_{{ tab_id }}" 
                               id="flavor-{{ tab_id }}-{{ flavor_id }}" 
                               value="{{ flavor }}"
                               {% if is_first_flavor %}checked{% endif %}>
                        <label for="flavor-{{ tab_id }}-{{ flavor_id }}">
                          <span class="flavor-image">
                            {%- if variant.featured_image -%}
                              <img src="{{ variant.featured_image | img_url: '100x100' }}" 
                                   alt="{{ flavor }}" 
                                   loading="lazy">
                            {%- else -%}
                              <img src="{{ product.featured_image | img_url: '100x100' }}" 
                                   alt="{{ flavor }}" 
                                   loading="lazy">
                            {%- endif -%}
                          </span>
                          <span class="flavor-name">{{ flavor }}</span>
                        </label>
                      </div>
                    {%- endunless -%}
                  {%- endif -%}
                {%- endfor -%}
              </div>
            </div>

            <!-- What's Included Section -->
            <div class="whats-included">
              <h3>What's Included:</h3>
              
              <div class="included-content">
                <div class="delivery-info">
                  <div class="delivery-frequency">
                    <span class="frequency-text">Every 30 Days</span>
                    <div class="frequency-images">
                      {%- for variant in product.variants -%}
                        {%- if variant.option1 == subscription_type -%}
                          {%- if variant.featured_image -%}
                            <img src="{{ variant.featured_image | img_url: '80x80' }}" 
                                 alt="{{ variant.option2 }}" 
                                 loading="lazy">
                          {%- endif -%}
                        {%- endif -%}
                      {%- endfor -%}
                    </div>
                  </div>
                  
                  <div class="onetime-info">
                    <span class="onetime-text">One Time <span class="free-text">(Free)</span></span>
                    <div class="onetime-images">
                      {%- for variant in product.variants -%}
                        {%- if variant.option1 == subscription_type -%}
                          {%- if variant.featured_image -%}
                            <img src="{{ variant.featured_image | img_url: '80x80' }}" 
                                 alt="{{ variant.option2 }}" 
                                 loading="lazy">
                          {%- endif -%}
                        {%- endif -%}
                      {%- endfor -%}
                    </div>
                  </div>
                </div>
                
                <!-- Benefits List -->
                <div class="benefits-list">
                  {%- comment -%} Get benefits from product metafields or use default {%- endcomment -%}
                  {%- assign benefits_key = subscription_type | handle | append: '_benefits' -%}
                  {%- assign benefits = product.metafields.custom[benefits_key] -%}
                  
                  {%- if benefits -%}
                    {%- assign benefits_array = benefits | split: '|' -%}
                    {%- for benefit in benefits_array -%}
                      <div class="benefit-item">
                        <span class="checkmark">✓</span>
                        <span class="benefit-text">{{ benefit | strip }}</span>
                      </div>
                    {%- endfor -%}
                  {%- else -%}
                    {%- comment -%} Default benefits if no metafields {%- endcomment -%}
                    <div class="benefit-item">
                      <span class="checkmark">✓</span>
                      <span class="benefit-text">Premium quality ingredients sourced globally</span>
                    </div>
                    <div class="benefit-item">
                      <span class="checkmark">✓</span>
                      <span class="benefit-text">Convenient monthly delivery to your doorstep</span>
                    </div>
                    <div class="benefit-item">
                      <span class="checkmark">✓</span>
                      <span class="benefit-text">Cancel or modify your subscription anytime</span>
                    </div>
                    <div class="benefit-item">
                      <span class="checkmark">✓</span>
                      <span class="benefit-text">Exclusive subscriber discounts and offers</span>
                    </div>
                    <div class="benefit-item">
                      <span class="checkmark">✓</span>
                      <span class="benefit-text">24/7 customer support for all your needs</span>
                    </div>
                  {%- endif -%}
                </div>
              </div>
            </div>

          </div>
        {%- endunless -%}
      {%- endfor -%}
    </div>

    <!-- Add to Cart Section -->
    <div class="add-to-cart-section">
      <form action="/cart/add" method="post" enctype="multipart/form-data" class="product-form">
        <input type="hidden" name="id" class="variant-id" value="">
        <button type="submit" class="add-to-cart-btn" disabled>
          <span class="btn-text">Add to Cart</span>
        </button>
      </form>
    </div>

  </div>

  <script>
    // Store product data for JavaScript
    window.productRecommendationsData = {
      productId: {{ product.id }},
      variants: [
        {%- for variant in product.variants -%}
          {
            id: {{ variant.id }},
            title: "{{ variant.title | escape }}",
            option1: "{{ variant.option1 | escape }}",
            option2: "{{ variant.option2 | escape }}",
            price: {{ variant.price }},
            compareAtPrice: {{ variant.compare_at_price | default: 0 }},
            available: {{ variant.available | json }},
            featuredImage: "{{ variant.featured_image | img_url: '300x300' }}"
          }{% unless forloop.last %},{% endunless %}
        {%- endfor -%}
      ]
    };
  </script>

{%- endif -%}

{% schema %}
{
  "name": "Product Subscription Tabs",
  "settings": [
    {
      "type": "product",
      "id": "product",
      "label": "Product"
    }
  ],
  "presets": [
    {
      "name": "Product Subscription Tabs"
    }
  ]
}
{% endschema %}
