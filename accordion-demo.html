<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Accordion Design Demo</title>
</head>
<body style="margin: 0; padding: 20px; background-color: #f5f5f5; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
    
    <h1 style="text-align: center; color: #333; margin-bottom: 30px;">Product Subscription Accordion Demo</h1>
    
    <!-- Include the accordion design -->
    <style>
/* Product Subscription Accordion - Clean Design */
.product-recommendations {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Recommended Header */
.recommendations-header {
  background-color: #B8956A;
  color: white;
  text-align: center;
  padding: 12px 20px;
  margin-bottom: 0;
  border-radius: 8px 8px 0 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0.5px;
}

/* Accordion Container */
.subscription-accordion {
  border: 1px solid #e0e0e0;
  border-top: none;
  background: white;
  border-radius: 0 0 8px 8px;
}

/* Accordion Item */
.accordion-item {
  border-bottom: 1px solid #e0e0e0;
}

.accordion-item:last-child {
  border-bottom: none;
}

/* Accordion Header (Clickable) */
.accordion-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  background: white;
}

.accordion-header:hover {
  background-color: #f8f8f8;
}

.accordion-item.active .accordion-header {
  background-color: #f0f0f0;
}

/* Radio Button */
.accordion-radio {
  width: 20px;
  height: 20px;
  border: 2px solid #ccc;
  border-radius: 50%;
  margin-right: 12px;
  position: relative;
  flex-shrink: 0;
  transition: border-color 0.2s ease;
}

.accordion-item.active .accordion-radio {
  border-color: #333;
}

.accordion-item.active .accordion-radio::after {
  content: '';
  width: 10px;
  height: 10px;
  background-color: #333;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* Accordion Title */
.accordion-title {
  flex: 1;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

/* Accordion Price */
.accordion-price {
  display: flex;
  align-items: center;
  gap: 8px;
}

.current-price {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.original-price {
  font-size: 14px;
  color: #999;
  text-decoration: line-through;
}

/* Accordion Content */
.accordion-content {
  display: none;
  padding: 20px;
  border-top: 1px solid #e0e0e0;
  background: #fafafa;
}

.accordion-item.active .accordion-content {
  display: block;
}

/* Flavor Selection */
.flavor-selection {
  margin-bottom: 30px;
}

.flavor-selection h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.flavor-options {
  display: flex;
  gap: 20px;
  justify-content: center;
}

.flavor-option {
  text-align: center;
  cursor: pointer;
}

.flavor-image {
  display: block;
  margin-bottom: 8px;
  border: 2px solid transparent;
  border-radius: 8px;
  overflow: hidden;
  transition: border-color 0.2s ease;
}

.flavor-option.selected .flavor-image {
  border-color: #333;
}

.flavor-image img {
  width: 80px;
  height: 80px;
  object-fit: cover;
  display: block;
}

.flavor-name {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.flavor-option.selected .flavor-name {
  color: #333;
}

/* What's Included Section */
.whats-included {
  margin-bottom: 30px;
}

.whats-included h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.delivery-info {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.delivery-frequency,
.onetime-info {
  flex: 1;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  text-align: center;
  background: white;
}

.frequency-text,
.onetime-text {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 12px;
}

.free-text {
  color: #B8956A;
  font-weight: 600;
}

.frequency-images,
.onetime-images {
  display: flex;
  justify-content: center;
  gap: 8px;
}

.frequency-images img,
.onetime-images img {
  width: 40px;
  height: 40px;
  object-fit: cover;
  border-radius: 4px;
}

/* Benefits List */
.benefits-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.benefit-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.checkmark {
  color: #4CAF50;
  font-weight: bold;
  font-size: 14px;
  margin-top: 2px;
  flex-shrink: 0;
}

.benefit-text {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

/* Add to Cart Button */
.add-to-cart-section {
  margin-top: 20px;
}

.add-to-cart-btn {
  width: 100%;
  background-color: #333;
  color: white;
  border: none;
  padding: 16px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.add-to-cart-btn:hover:not(:disabled) {
  background-color: #555;
}

.add-to-cart-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
  .product-recommendations {
    padding: 16px;
  }
  
  .flavor-options {
    gap: 12px;
  }
  
  .flavor-image img {
    width: 60px;
    height: 60px;
  }
  
  .delivery-info {
    flex-direction: column;
    gap: 12px;
  }
  
  .accordion-header {
    padding: 12px 16px;
  }
  
  .accordion-title {
    font-size: 14px;
  }
  
  .current-price {
    font-size: 16px;
  }
}
</style>

<div class="product-recommendations">
  <!-- Recommended Header -->
  <div class="recommendations-header">
    Recommended
  </div>

  <!-- Accordion Container -->
  <div class="subscription-accordion">
    
    <!-- Single Drink Subscription -->
    <div class="accordion-item active" data-subscription="single">
      <div class="accordion-header" onclick="toggleAccordion(this)">
        <div class="accordion-radio"></div>
        <div class="accordion-title">Single Drink Subscription</div>
        <div class="accordion-price">
          <span class="current-price">$6.00</span>
          <span class="original-price">$12.00</span>
        </div>
      </div>
      
      <div class="accordion-content">
        <!-- Flavor Selection -->
        <div class="flavor-selection">
          <h3>Choose Flavor</h3>
          <div class="flavor-options">
            <div class="flavor-option selected" data-flavor="chocolate">
              <div class="flavor-image">
                <img src="https://via.placeholder.com/80x80/8B4513/FFFFFF?text=C" alt="Chocolate">
              </div>
              <div class="flavor-name">Chocolate</div>
            </div>
            <div class="flavor-option" data-flavor="vanilla">
              <div class="flavor-image">
                <img src="https://via.placeholder.com/80x80/F5F5DC/000000?text=V" alt="Vanilla">
              </div>
              <div class="flavor-name">Vanilla</div>
            </div>
            <div class="flavor-option" data-flavor="orange">
              <div class="flavor-image">
                <img src="https://via.placeholder.com/80x80/FFA500/FFFFFF?text=O" alt="Orange">
              </div>
              <div class="flavor-name">Orange</div>
            </div>
          </div>
        </div>

        <!-- What's Included -->
        <div class="whats-included">
          <h3>What's Included:</h3>
          
          <div class="delivery-info">
            <div class="delivery-frequency">
              <span class="frequency-text">Every 30 Days</span>
              <div class="frequency-images">
                <img src="https://via.placeholder.com/40x40/8B4513/FFFFFF?text=C" alt="Product">
                <img src="https://via.placeholder.com/40x40/F5F5DC/000000?text=V" alt="Product">
                <img src="https://via.placeholder.com/40x40/FFA500/FFFFFF?text=O" alt="Product">
              </div>
            </div>
            
            <div class="onetime-info">
              <span class="onetime-text">One Time <span class="free-text">(Free)</span></span>
              <div class="onetime-images">
                <img src="https://via.placeholder.com/40x40/8B4513/FFFFFF?text=C" alt="Product">
                <img src="https://via.placeholder.com/40x40/F5F5DC/000000?text=V" alt="Product">
                <img src="https://via.placeholder.com/40x40/FFA500/FFFFFF?text=O" alt="Product">
              </div>
            </div>
          </div>
          
          <!-- Benefits List -->
          <div class="benefits-list">
            <div class="benefit-item">
              <span class="checkmark">✓</span>
              <span class="benefit-text">Lorem ipsum dolor sit amet, consectetur adipiscing elit</span>
            </div>
            <div class="benefit-item">
              <span class="checkmark">✓</span>
              <span class="benefit-text">Lorem ipsum dolor sit amet, consectetur adipiscing elit</span>
            </div>
            <div class="benefit-item">
              <span class="checkmark">✓</span>
              <span class="benefit-text">Lorem ipsum dolor sit amet, consectetur adipiscing elit</span>
            </div>
            <div class="benefit-item">
              <span class="checkmark">✓</span>
              <span class="benefit-text">Lorem ipsum dolor sit amet, consectetur adipiscing elit</span>
            </div>
            <div class="benefit-item">
              <span class="checkmark">✓</span>
              <span class="benefit-text">Lorem ipsum dolor sit amet, consectetur adipiscing elit</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Double Drink Subscription -->
    <div class="accordion-item" data-subscription="double">
      <div class="accordion-header" onclick="toggleAccordion(this)">
        <div class="accordion-radio"></div>
        <div class="accordion-title">Double Drink Subscription</div>
        <div class="accordion-price">
          <span class="current-price">$12.00</span>
          <span class="original-price">$24.00</span>
        </div>
      </div>
      
      <div class="accordion-content">
        <!-- Same content structure as above -->
        <div class="flavor-selection">
          <h3>Choose Flavor</h3>
          <div class="flavor-options">
            <div class="flavor-option selected" data-flavor="chocolate">
              <div class="flavor-image">
                <img src="https://via.placeholder.com/80x80/8B4513/FFFFFF?text=C" alt="Chocolate">
              </div>
              <div class="flavor-name">Chocolate</div>
            </div>
            <div class="flavor-option" data-flavor="vanilla">
              <div class="flavor-image">
                <img src="https://via.placeholder.com/80x80/F5F5DC/000000?text=V" alt="Vanilla">
              </div>
              <div class="flavor-name">Vanilla</div>
            </div>
            <div class="flavor-option" data-flavor="orange">
              <div class="flavor-image">
                <img src="https://via.placeholder.com/80x80/FFA500/FFFFFF?text=O" alt="Orange">
              </div>
              <div class="flavor-name">Orange</div>
            </div>
          </div>
        </div>

        <div class="whats-included">
          <h3>What's Included:</h3>
          <div class="delivery-info">
            <div class="delivery-frequency">
              <span class="frequency-text">Every 30 Days</span>
              <div class="frequency-images">
                <img src="https://via.placeholder.com/40x40/8B4513/FFFFFF?text=C" alt="Product">
                <img src="https://via.placeholder.com/40x40/F5F5DC/000000?text=V" alt="Product">
                <img src="https://via.placeholder.com/40x40/FFA500/FFFFFF?text=O" alt="Product">
              </div>
            </div>
            <div class="onetime-info">
              <span class="onetime-text">One Time <span class="free-text">(Free)</span></span>
              <div class="onetime-images">
                <img src="https://via.placeholder.com/40x40/8B4513/FFFFFF?text=C" alt="Product">
                <img src="https://via.placeholder.com/40x40/F5F5DC/000000?text=V" alt="Product">
                <img src="https://via.placeholder.com/40x40/FFA500/FFFFFF?text=O" alt="Product">
              </div>
            </div>
          </div>
          <div class="benefits-list">
            <div class="benefit-item">
              <span class="checkmark">✓</span>
              <span class="benefit-text">Lorem ipsum dolor sit amet, consectetur adipiscing elit</span>
            </div>
            <div class="benefit-item">
              <span class="checkmark">✓</span>
              <span class="benefit-text">Lorem ipsum dolor sit amet, consectetur adipiscing elit</span>
            </div>
            <div class="benefit-item">
              <span class="checkmark">✓</span>
              <span class="benefit-text">Lorem ipsum dolor sit amet, consectetur adipiscing elit</span>
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>

  <!-- Add to Cart Button -->
  <div class="add-to-cart-section">
    <button class="add-to-cart-btn">
      Add to Cart
    </button>
  </div>
</div>

<script>
// Basic Accordion Functionality - Step 1
function toggleAccordion(header) {
  const accordionItem = header.parentElement;
  const allItems = document.querySelectorAll('.accordion-item');
  
  // Close all other accordions
  allItems.forEach(item => {
    if (item !== accordionItem) {
      item.classList.remove('active');
    }
  });
  
  // Toggle current accordion
  accordionItem.classList.add('active');
}

// Flavor Selection
document.addEventListener('DOMContentLoaded', function() {
  const flavorOptions = document.querySelectorAll('.flavor-option');
  
  flavorOptions.forEach(option => {
    option.addEventListener('click', function() {
      // Remove selected class from siblings
      const siblings = this.parentElement.querySelectorAll('.flavor-option');
      siblings.forEach(sibling => sibling.classList.remove('selected'));
      
      // Add selected class to clicked option
      this.classList.add('selected');
    });
  });
});
</script>

</body>
</html>
